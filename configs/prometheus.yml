# PaaS 平台 Prometheus 配置文件
# 定义了指标收集、存储和告警规则

# 全局配置
global:
  # 抓取间隔
  scrape_interval: 15s
  # 评估间隔
  evaluation_interval: 15s
  # 外部标签
  external_labels:
    cluster: 'paas-platform'
    environment: 'production'
    region: 'local'

# 告警规则文件
rule_files:
  - "alert-rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    scrape_interval: 15s
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics

  # PaaS 平台核心服务
  - job_name: 'paas-services'
    scrape_interval: 15s
    metrics_path: '/metrics'
    static_configs:
      - targets:
          # 🔐 用户认证服务
          - 'host.docker.internal:8083'
          # 📱 应用管理服务
          - 'host.docker.internal:8081'
          # 🌐 API 网关
          - 'host.docker.internal:8080'
          # 🔧 CI/CD 服务
          - 'host.docker.internal:8082'
          # ⚙️ 配置服务
          - 'host.docker.internal:8084'
          # 📜 脚本服务
          - 'host.docker.internal:8084'
    
    # 服务发现和标签重写
    relabel_configs:
      # 提取服务名称
      - source_labels: [__address__]
        regex: 'host\.docker\.internal:(\d+)'
        target_label: __tmp_port
      
      # 根据端口设置服务名称
      - source_labels: [__tmp_port]
        regex: '8083'
        target_label: service
        replacement: 'user-service'
      - source_labels: [__tmp_port]
        regex: '8081'
        target_label: service
        replacement: 'app-manager'
      - source_labels: [__tmp_port]
        regex: '8080'
        target_label: service
        replacement: 'api-gateway'
      - source_labels: [__tmp_port]
        regex: '8082'
        target_label: service
        replacement: 'cicd-service'
      - source_labels: [__tmp_port]
        regex: '8084'
        target_label: service
        replacement: 'config-service'
      
      # 设置实例标签
      - source_labels: [__address__]
        target_label: instance
      
      # 添加环境标签
      - target_label: environment
        replacement: 'development'

  # 系统指标 - Node Exporter
  - job_name: 'node-exporter'
    scrape_interval: 15s
    static_configs:
      - targets: ['node-exporter:9100']
    relabel_configs:
      - target_label: service
        replacement: 'node-exporter'
      - target_label: component
        replacement: 'system'

  # 数据库指标 - PostgreSQL Exporter
  - job_name: 'postgres-exporter'
    scrape_interval: 30s
    static_configs:
      - targets: ['postgres-exporter:9187']
    relabel_configs:
      - target_label: service
        replacement: 'postgresql'
      - target_label: component
        replacement: 'database'

  # 缓存指标 - Redis Exporter
  - job_name: 'redis-exporter'
    scrape_interval: 30s
    static_configs:
      - targets: ['redis-exporter:9121']
    relabel_configs:
      - target_label: service
        replacement: 'redis'
      - target_label: component
        replacement: 'cache'

  # 容器指标 - cAdvisor
  - job_name: 'cadvisor'
    scrape_interval: 15s
    static_configs:
      - targets: ['cadvisor:8080']
    relabel_configs:
      - target_label: service
        replacement: 'cadvisor'
      - target_label: component
        replacement: 'container'

  # 监控系统自身
  - job_name: 'monitoring-stack'
    scrape_interval: 30s
    static_configs:
      - targets:
          - 'prometheus:9090'
          - 'grafana:3000'
          - 'alertmanager:9093'
    relabel_configs:
      - source_labels: [__address__]
        regex: '([^:]+):(\d+)'
        target_label: service
        replacement: '${1}'
      - target_label: component
        replacement: 'monitoring'

  # 前端应用监控 (如果有指标端点)
  - job_name: 'frontend'
    scrape_interval: 30s
    metrics_path: '/api/metrics'
    static_configs:
      - targets: ['host.docker.internal:3000']
    relabel_configs:
      - target_label: service
        replacement: 'frontend'
      - target_label: component
        replacement: 'web'
    # 可选：如果前端没有指标端点，可以注释掉这个job
    scrape_timeout: 10s

# 远程写入配置 (可选)
# remote_write:
#   - url: "http://remote-storage:9201/write"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# 远程读取配置 (可选)
# remote_read:
#   - url: "http://remote-storage:9201/read"

# 存储配置
storage:
  tsdb:
    # 数据保留时间
    retention_time: "30d"
    # 数据保留大小
    retention_size: "10GB"
    # 压缩级别
    wal_compression: true

# 查询配置
query:
  # 查询超时
  timeout: 2m
  # 最大并发查询
  max_concurrency: 20
  # 最大样本数
  max_samples: 50000000

# 日志配置
log:
  level: info
  format: logfmt

# Web 配置
web:
  # 监听地址
  listen_address: "0.0.0.0:9090"
  # 外部 URL
  external_url: "http://localhost:9090"
  # 启用生命周期 API
  enable_lifecycle: true
  # 启用管理 API
  enable_admin_api: true

# 运行时配置
runtime:
  # Go 最大进程数
  gomaxprocs: 4
  # 内存限制 (可选)
  # memory_limit: "2GB"

---
# 📝 配置说明
# 
# 本配置文件定义了 PaaS 平台的 Prometheus 监控配置：
# 
# 1. 🎯 抓取目标：
#    - PaaS 平台所有微服务 (8080-8084)
#    - 系统指标 (Node Exporter)
#    - 数据库指标 (PostgreSQL Exporter)
#    - 缓存指标 (Redis Exporter)
#    - 容器指标 (cAdvisor)
# 
# 2. 📊 指标收集：
#    - 15秒间隔收集应用指标
#    - 30秒间隔收集基础设施指标
#    - 自动服务发现和标签重写
# 
# 3. 🚨 告警集成：
#    - 集成 AlertManager
#    - 支持告警规则文件
# 
# 4. 🔧 性能优化：
#    - 数据压缩和保留策略
#    - 查询并发和超时控制
# 
# 使用此配置可以实现完整的指标收集和监控。
